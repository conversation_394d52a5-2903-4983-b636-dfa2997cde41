{"version": 3, "sources": ["../templates/startDevWorker/InspectorProxyWorker.ts", "../src/api/startDevWorker/events.ts", "../src/api/startDevWorker/utils.ts"], "mappings": ";AAAA,OAAOA,aAAY;;;ACiIZ,SAAS,eAAe,GAA6B;AAC3D,MAAI,aAAa,OAAO;AACvB,WAAO;AAAA,MACN,SAAS,EAAE;AAAA,MACX,MAAM,EAAE;AAAA,MACR,OAAO,EAAE;AAAA,MACT,OAAO,EAAE,SAAS,eAAe,EAAE,KAAK;AAAA,IACzC;AAAA,EACD,OAAO;AACN,WAAO,EAAE,SAAS,OAAO,CAAC,EAAE;AAAA,EAC7B;AACD;;;AC5IA,OAAO,YAAY;AAoBZ,SAAS,eACf,kBACqB;AACrB,MAAI,SAAS;AACb,QAAM,aAAa,IAAI,QAAW,CAAC,UAAU,YAAY;AACxD,cAAU;AACV,aAAS;AAAA,EACV,CAAC;AACD,SAAO,OAAO;AACd,SAAO,MAAM;AAIb,oBAAkB,QAAQ,UAAU;AAEpC,SAAO;AAAA,IACN,SAAS;AAAA,IACT;AAAA,IACA;AAAA,EACD;AACD;AAEO,SAAS,YAAY,QAAe;AAAC;AAErC,SAAS,aACf,OACA,OAAO,oBACD;AACN,QAAM,MAAM,IAAI,IAAI,IAAI;AAExB,SAAO,OAAO,KAAK,KAAK;AAExB,SAAO;AACR;;;AF9BA,IAAM,yBAAyB,CAAC,aAAa,SAAS,WAAW;AACjE,IAAM,2BAA2B;AAAA,EAChC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AASA,IAAO,+BAAQ;AAAA,EACd,MAAM,KAAK,KAAK;AACf,UAAM,YAAY,IAAI,eAAe,WAAW,EAAE;AAClD,UAAM,iBAAiB,IAAI,eAAe,IAAI,SAAS;AAEvD,WAAO,eAAe,MAAM,GAAG;AAAA,EAChC;AACD;AAEA,SAAS,gBACR,OACA,MACiC;AACjC,SACC,OAAO,UAAU,YACjB,UAAU,QACV,YAAY,SACZ,MAAM,WAAW;AAEnB;AAEO,IAAM,uBAAN,MAAoD;AAAA,EAC1D,YACC,QACS,KACR;AADQ;AAAA,EACP;AAAA,EAEH,aAcI;AAAA,IACH,iBAAiB,eAA0B;AAAA,EAC5C;AAAA,EACA;AAAA,EACA,uBAAsE,CAAC;AAAA,EAEvE,MAAM,MAAM,KAAc;AACzB,QACC,IAAI,QAAQ,IAAI,eAAe,MAAM,KAAK,IAAI,8BAC7C;AACD,aAAO,KAAK,6BAA6B,GAAG;AAAA,IAC7C;AAEA,QAAI,IAAI,QAAQ,IAAI,SAAS,MAAM,aAAa;AAC/C,aAAO,KAAK,sCAAsC,GAAG;AAAA,IACtD;AAEA,WAAO,KAAK,0BAA0B,GAAG;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA,EAMA,6BAA6B,KAAc;AAC1C,IAAAC;AAAA,MACC,IAAI,QAAQ,IAAI,SAAS,MAAM;AAAA,MAC/B;AAAA,IACD;AAEA,UAAM,EAAE,GAAG,UAAU,GAAG,gBAAgB,IAAI,IAAI,cAAc;AAC9D,oBAAgB,OAAO;AACvB,oBAAgB,iBAAiB,SAAS,CAAC,UAAU;AAIpD,WAAK;AAAA,QACJ;AAAA,QACA,MAAM;AAAA,QACN,MAAM;AAAA,MACP;AAEA,UAAI,KAAK,WAAW,oBAAoB,iBAAiB;AACxD,aAAK,WAAW,kBAAkB;AAAA,MACnC;AAAA,IACD,CAAC;AACD,oBAAgB,iBAAiB,SAAS,CAAC,UAAU;AAIpD,YAAM,QAAQ,eAAe,MAAM,KAAK;AACxC,WAAK,aAAa,oCAAoC,KAAK;AAE3D,UAAI,KAAK,WAAW,oBAAoB,iBAAiB;AACxD,aAAK,WAAW,kBAAkB;AAAA,MACnC;AAAA,IACD,CAAC;AACD,oBAAgB;AAAA,MACf;AAAA,MACA,KAAK;AAAA,IACN;AAEA,SAAK,WAAW,kBAAkB;AAElC,WAAO,IAAI,SAAS,MAAM;AAAA,MACzB,QAAQ;AAAA,MACR,WAAW;AAAA,IACZ,CAAC;AAAA,EACF;AAAA,EAEA,uCAAuC,CAAC,UAAwB;AAC/D,IAAAA;AAAA,MACC,OAAO,MAAM,SAAS;AAAA,MACtB;AAAA,IACD;AAEA,UAAM,UAAwD,KAAK;AAAA,MAClE,MAAM;AAAA,IACP;AAEA,SAAK,aAAa,wCAAwC,MAAM,IAAI;AAEpE,YAAQ,QAAQ,MAAM;AAAA,MACrB,KAAK,eAAe;AACnB,aAAK,iCAAiC;AAEtC;AAAA,MACD;AAAA,MACA,KAAK,kBAAkB;AACtB,aAAK,YAAY,QAAQ;AAEzB,aAAK,0BAA0B;AAE/B;AAAA,MACD;AAAA,MACA,SAAS;AACR,oBAAY,OAAO;AAAA,MACpB;AAAA,IACD;AAAA,EACD;AAAA,EAEA,2BACC,SACC;AACD,cAAU,OAAO,YAAY,WAAW,UAAU,KAAK,UAAU,OAAO;AAGxE,SAAK,WAAW,iBAAiB,KAAK,OAAO;AAAA,EAC9C;AAAA,EAEA,MAAM,2BACL,SACC;AACD,QAAI;AACH,YAAM,MAAM,MAAM,KAAK,IAAI,iBAAiB,MAAM,gBAAgB;AAAA,QACjE,QAAQ;AAAA,QACR,MAAM,KAAK,UAAU,OAAO;AAAA,MAC7B,CAAC;AACD,aAAO,IAAI,KAAK,MAAM,IAAI,KAAK,IAAI;AAAA,IACpC,SAAS,GAAP;AACD,WAAK;AAAA,QACJ;AAAA,QACA,eAAe,CAAC;AAAA,MACjB;AACA,aAAO;AAAA,IACR;AAAA,EACD;AAAA,EAEA,eAAqC,IAAI,SAAS;AACjD,SAAK,2BAA2B,EAAE,MAAM,aAAa,KAAK,CAAC;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA,EAMA,+BAA+B,CAAC,UAAwB;AACvD,IAAAA,QAAO,OAAO,MAAM,SAAS,QAAQ;AAErC,UAAM,MAAM,KAAK,MAAM,MAAM,IAAI;AAGjC,SAAK,aAAa,4BAA4B,GAAG;AAEjD,QAAI,gBAAgB,KAAK,yBAAyB,GAAG;AACpD,WAAK,2BAA2B,MAAM,IAAI;AAAA,IAC3C;AACA,QACC,KAAK,WAAW,yBAChB,gBAAgB,KAAK,0BAA0B,GAC9C;AACD,WAAK,2BAA2B,MAAM,IAAI;AAAA,IAC3C;AAEA,SAAK,qBAAqB,KAAK,GAAG;AAClC,SAAK,6BAA6B;AAAA,EACnC;AAAA,EAEA,0BAA0B,KAA6C;AAMtE,QACC,CAAC,KAAK,WAAW,+BACjB,IAAI,OAAO,iBAAiB;AAAA,IAE5B,IAAI,OAAO,IAAI,WAAW,OAAO,GAChC;AACD,YAAM,MAAM,IAAI,IAAI,IAAI,OAAO,cAAc,IAAI,OAAO,GAAG;AAG3D,UAAI,IAAI,aAAa,SAAS;AAC7B,YAAI,OAAO,eAAe,IAAI,KAAK,QAAQ,SAAS,gBAAgB;AAAA,MACrE;AAAA,IACD;AAEA,SAAK,KAAK,oBAAoB,GAAG;AAAA,EAClC;AAAA,EAEA,+BAA+B,MAAM;AAEpC,QAAI,KAAK,WAAW,aAAa;AAAW;AAG5C,eAAW,OAAO,KAAK,qBAAqB,OAAO,CAAC,GAAG;AACtD,UAAI,gBAAgB,KAAK,uBAAuB,GAAG;AAClD,aAAK,0BAA0B,GAAG;AAAA,MACnC,OAAO;AACN,aAAK,KAAK,oBAAoB,GAAG;AAAA,MAClC;AAAA,IACD;AAAA,EACD;AAAA,EAEA,yBAAyB,IAAI,gBAAgB;AAAA;AAAA,EAC7C,2BAA0C;AAAA,EAC1C,MAAM,4BAA4B;AACjC,IAAAA,QAAO,KAAK,WAAW,uCAAuC;AAE9D,SAAK,aAAa,2BAA2B;AAE7C,SAAK,WAAW,SAAS,MAAM;AAC/B,SAAK,WAAW,UAAU;AAC1B,SAAK,uBAAuB,MAAM;AAClC,SAAK,yBAAyB,IAAI,gBAAgB;AAClD,SAAK,WAAW,kBAAkB;AAAA,MACjC,KAAK,WAAW;AAAA,IACjB;AAEA,UAAM,sBAAsB;AAAA,MAC3B,KAAK,UAAU;AAAA,IAChB;AACA,wBAAoB,WAAW,KAAK,UAAU,cAAc;AAE5D,SAAK,aAAa,yBAAyB,mBAAmB;AAI9D,SAAK,oBAAoB;AAAA,MACxB,QAAQ;AAAA,MACR,QAAQ;AAAA,IACT,CAAC;AAED,UAAM,UAAU,MAAM,MAAM,qBAAqB;AAAA,MAChD,SAAS;AAAA,QACR,GAAG,KAAK,UAAU;AAAA,QAClB,SAAS;AAAA,MACV;AAAA,MACA,QAAQ,KAAK,uBAAuB;AAAA,IACrC,CAAC;AAED,UAAM,UAAU,QAAQ;AACxB,QAAI,CAAC,SAAS;AACb,YAAM,QAAQ,IAAI;AAAA,QACjB,wIAAwI,QAAQ;AAAA,MACjJ;AAEA,WAAK,WAAW,gBAAgB,OAAO,KAAK;AAC5C,WAAK,2BAA2B;AAAA,QAC/B,MAAM;AAAA,QACN,OAAO,eAAe,KAAK;AAAA,MAC5B,CAAC;AAED;AAAA,IACD;AAEA,SAAK,WAAW,UAAU;AAE1B,YAAQ,iBAAiB,WAAW,KAAK,4BAA4B;AAErE,YAAQ,iBAAiB,SAAS,CAAC,UAAU;AAC5C,WAAK,aAAa,4BAA4B,MAAM,MAAM,MAAM,MAAM;AAEtE,oBAAc,KAAK,wBAAwB;AAE3C,UAAI,KAAK,WAAW,YAAY,SAAS;AACxC,aAAK,WAAW,UAAU;AAAA,MAC3B;AAAA,IAKD,CAAC;AAED,YAAQ,iBAAiB,SAAS,CAAC,UAAU;AAC5C,YAAM,QAAQ,eAAe,MAAM,KAAK;AACxC,WAAK,aAAa,2BAA2B,KAAK;AAElD,oBAAc,KAAK,wBAAwB;AAE3C,UAAI,KAAK,WAAW,YAAY,SAAS;AACxC,aAAK,WAAW,UAAU;AAAA,MAC3B;AAEA,WAAK,2BAA2B;AAAA,QAC/B,MAAM;AAAA,QACN;AAAA,MACD,CAAC;AAAA,IAKF,CAAC;AAED,YAAQ,OAAO;AAIf,SAAK,2BAA2B,OAAO;AAAA,EACxC;AAAA,EAEA,yBAAyB;AAAA,EACzB,cAAc;AACb,WAAO,EAAE,KAAK;AAAA,EACf;AAAA,EACA,2BAA2B,SAAoB;AAC9C,SAAK,aAAa,0BAA0B;AAE5C,SAAK;AAAA,MACJ,EAAE,QAAQ,kBAAkB,IAAI,KAAK,YAAY,EAAE;AAAA,MACnD;AAAA,IACD;AACA,SAAK;AAAA,MACJ,EAAE,QAAQ,mBAAmB,IAAI,KAAK,YAAY,EAAE;AAAA,MACpD;AAAA,IACD;AACA,SAAK;AAAA,MACJ,EAAE,QAAQ,kBAAkB,IAAI,KAAK,YAAY,EAAE;AAAA,MACnD;AAAA,IACD;AAEA,kBAAc,KAAK,wBAAwB;AAC3C,SAAK,2BAA2B,YAAY,MAAM;AACjD,WAAK;AAAA,QACJ,EAAE,QAAQ,wBAAwB,IAAI,KAAK,YAAY,EAAE;AAAA,QACzD;AAAA,MACD;AAAA,IACD,GAAG,GAAM;AAET,SAAK,WAAW,gBAAgB,QAAQ,OAAO;AAAA,EAChD;AAAA,EAEA,mCAAmC;AAMlC,QAAI,KAAK,WAAW,SAAS;AAC5B,WAAK;AAAA,QACJ;AAAA,UACC,QAAQ;AAAA,UACR,IAAI,KAAK,YAAY;AAAA,QACtB;AAAA,QACA,KAAK,WAAW;AAAA,MACjB;AAAA,IACD;AAAA,EACD;AAAA,EAEA,MAAM,mBACL,SACA,UAAmC,KAAK,WAAW,gBAAgB,SAClE;AACD,cAAU,MAAM;AAChB,cAAU,OAAO,YAAY,WAAW,UAAU,KAAK,UAAU,OAAO;AAExE,SAAK,aAAa,mBAAmB,OAAO;AAE5C,YAAQ,KAAK,OAAO;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe,OAAO,WAAW;AAAA,EACjC,MAAM,0BAA0B,KAAc;AAC7C,UAAM,MAAM,IAAI,IAAI,IAAI,GAAG;AAE3B,QAAI,IAAI,aAAa,iBAAiB;AACrC,aAAO,SAAS,KAAK;AAAA,QACpB,SAAS,aAAa,KAAK,IAAI;AAAA;AAAA;AAAA,QAG/B,oBAAoB;AAAA,MACrB,CAAC;AAAA,IACF;AAEA,QAAI,IAAI,aAAa,WAAW,IAAI,aAAa,cAAc;AAE9D,YAAM,YAAY,GAAG,IAAI;AACzB,YAAM,sBAAsB,yFAAyF;AAErH,aAAO,SAAS,KAAK;AAAA,QACpB;AAAA,UACC,IAAI,KAAK;AAAA,UACT,MAAM;AAAA;AAAA,UACN,aAAa;AAAA,UACb,sBAAsB,QAAQ;AAAA,UAC9B;AAAA,UACA,2BAA2B;AAAA;AAAA,UAE3B,OAAO;AAAA,UACP,YAAY;AAAA;AAAA,QAEb;AAAA,MACD,CAAC;AAAA,IACF;AAEA,WAAO,IAAI,SAAS,MAAM,EAAE,QAAQ,IAAI,CAAC;AAAA,EAC1C;AAAA,EAEA,MAAM,sCAAsC,KAAc;AAEzD,QAAI,aAAa,IAAI,QAAQ,IAAI,MAAM;AACvC,QAAI,cAAc;AAAM,aAAO,IAAI,SAAS,MAAM,EAAE,QAAQ,IAAI,CAAC;AACjE,QAAI;AACH,YAAM,OAAO,IAAI,IAAI,UAAU,YAAY;AAC3C,UAAI,CAAC,uBAAuB,SAAS,KAAK,QAAQ,GAAG;AACpD,eAAO,IAAI,SAAS,4BAA4B,EAAE,QAAQ,IAAI,CAAC;AAAA,MAChE;AAAA,IACD,QAAE;AACD,aAAO,IAAI,SAAS,0BAA0B,EAAE,QAAQ,IAAI,CAAC;AAAA,IAC9D;AAEA,QAAI,eAAe,IAAI,QAAQ,IAAI,QAAQ;AAC3C,QAAI,iBAAiB,QAAQ,CAAC,IAAI,QAAQ,IAAI,YAAY,GAAG;AAG5D,qBAAe;AAAA,IAChB;AACA,QAAI,iBAAiB,MAAM;AAC1B,aAAO,IAAI,SAAS,4BAA4B,EAAE,QAAQ,IAAI,CAAC;AAAA,IAChE;AACA,QAAI;AACH,YAAM,SAAS,IAAI,IAAI,YAAY;AACnC,YAAM,UAAU,yBAAyB,KAAK,CAAC,SAAS;AACvD,YAAI,OAAO,SAAS;AAAU,iBAAO,OAAO,aAAa;AAAA;AACpD,iBAAO,KAAK,KAAK,OAAO,QAAQ;AAAA,MACtC,CAAC;AACD,UAAI,CAAC,SAAS;AACb,eAAO,IAAI,SAAS,8BAA8B,EAAE,QAAQ,IAAI,CAAC;AAAA,MAClE;AAAA,IACD,QAAE;AACD,aAAO,IAAI,SAAS,4BAA4B,EAAE,QAAQ,IAAI,CAAC;AAAA,IAChE;AAGA,SAAK,aAAa,sCAAsC;AAGxD,UAAM,KAAK,WAAW,gBAAgB;AAEtC,SAAK,aAAa,oCAAoC;AAEtD,IAAAA;AAAA,MACC,IAAI,QAAQ,IAAI,SAAS,MAAM;AAAA,MAC/B;AAAA,IACD;AACA,UAAM,EAAE,GAAG,UAAU,GAAG,SAAS,IAAI,IAAI,cAAc;AACvD,aAAS,OAAO;AAEhB,QAAI,KAAK,WAAW,aAAa,QAAW;AAG3C,eAAS;AAAA,QACR;AAAA,QACA;AAAA,MACD;AAAA,IACD,OAAO;AACN,eAAS,iBAAiB,WAAW,KAAK,6BAA6B;AACvE,eAAS,iBAAiB,SAAS,CAAC,UAAU;AAC7C,aAAK;AAAA,UACJ;AAAA,UACA,MAAM;AAAA,UACN,MAAM;AAAA,QACP;AAEA,YAAI,KAAK,WAAW,aAAa,UAAU;AAC1C,eAAK,WAAW,WAAW;AAAA,QAC5B;AAAA,MACD,CAAC;AACD,eAAS,iBAAiB,SAAS,CAAC,UAAU;AAC7C,cAAM,QAAQ,eAAe,MAAM,KAAK;AACxC,aAAK,aAAa,4BAA4B,KAAK;AAEnD,YAAI,KAAK,WAAW,aAAa,UAAU;AAC1C,eAAK,WAAW,WAAW;AAAA,QAC5B;AAAA,MACD,CAAC;AAMD,WAAK,mBAAmB;AAAA,QACvB,IAAI,KAAK,YAAY;AAAA,QACrB,QAAQ;AAAA,MACT,CAAC;AAED,WAAK,aAAa,8BAA8B;AAsChD,YAAM,YAAY,IAAI,QAAQ,IAAI,YAAY,KAAK;AACnD,YAAM,sBAAsB,CAAC,WAAW,KAAK,SAAS;AAEtD,WAAK,WAAW,WAAW;AAC3B,WAAK,WAAW,8BAA8B;AAE9C,WAAK,6BAA6B;AAAA,IACnC;AAEA,WAAO,IAAI,SAAS,MAAM,EAAE,QAAQ,KAAK,WAAW,SAAS,CAAC;AAAA,EAC/D;AAAA,EAEA,gCAAgC,CAAC,UAAwB;AACxD,IAAAA;AAAA,MACC,OAAO,MAAM,SAAS;AAAA,MACtB;AAAA,IACD;AAEA,UAAM,UAAU,KAAK,MAAM,MAAM,IAAI;AACrC,SAAK,aAAa,6BAA6B,OAAO;AAEtD,QAAI,QAAQ,WAAW,+BAA+B;AACrD,aAAO,KAAK,KAAK,kCAAkC,OAAO;AAAA,IAC3D;AAEA,SAAK,mBAAmB,KAAK,UAAU,OAAO,CAAC;AAAA,EAChD;AAAA,EAEA,MAAM,kCACL,SACC;AACD,UAAM,WAAW,MAAM,KAAK,2BAA2B;AAAA,MACtD,MAAM;AAAA,MACN,KAAK,QAAQ,OAAO;AAAA,IACrB,CAAC;AACD,QAAI,aAAa,QAAW;AAC3B,WAAK;AAAA,QACJ,sEAAsE,QAAQ,OAAO;AAAA,MACtF;AAGA,WAAK,mBAAmB,KAAK,UAAU,OAAO,CAAC;AAAA,IAChD,OAAO;AAIN,WAAK,oBAAoB;AAAA,QACxB,IAAI,QAAQ;AAAA;AAAA,QAEZ,QAAQ,EAAE,UAAU,EAAE,SAAS,MAAM,MAAM,SAAS,EAAE;AAAA,MACvD,CAAC;AAAA,IACF;AAAA,EACD;AAAA,EAEA,oBACC,SACC;AACD,cAAU,OAAO,YAAY,WAAW,UAAU,KAAK,UAAU,OAAO;AAExE,SAAK,aAAa,oBAAoB,OAAO;AAE7C,SAAK,WAAW,UAAU,KAAK,OAAO;AAAA,EACvC;AACD;", "names": ["assert", "assert"]}