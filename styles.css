/* 基础样式和字体 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'JetBrains Mono', monospace;
    background: #000;
    color: #00ff00;
    overflow: hidden;
    cursor: crosshair;
}

/* 页面切换 */
.page {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    opacity: 0;
    visibility: hidden;
    transition: all 0.5s ease;
}

.page.active {
    opacity: 1;
    visibility: visible;
}

/* CRT显示器效果 */
.crt-container {
    width: 100%;
    height: 100%;
    background: radial-gradient(ellipse at center, #001100 0%, #000000 100%);
    position: relative;
    overflow: hidden;
}

.crt-effect {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 2rem;
}

/* 扫描线效果 */
.scanlines {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: repeating-linear-gradient(
        0deg,
        transparent,
        transparent 2px,
        rgba(0, 255, 0, 0.03) 2px,
        rgba(0, 255, 0, 0.03) 4px
    );
    pointer-events: none;
    animation: scanline-move 0.1s linear infinite;
}

@keyframes scanline-move {
    0% { transform: translateY(0); }
    100% { transform: translateY(4px); }
}

/* 像素心电图 */
.pixel-heart {
    width: 200px;
    height: 200px;
    position: relative;
    margin-bottom: 3rem;
}

.heart-beat {
    width: 100%;
    height: 100%;
    background: #00ff00;
    clip-path: polygon(
        50% 25%, 
        60% 5%, 80% 5%, 100% 30%, 
        100% 50%, 75% 75%, 50% 100%, 
        25% 75%, 0% 50%, 0% 30%, 
        20% 5%, 40% 5%
    );
    animation: heartbeat 1.5s ease-in-out infinite;
    filter: drop-shadow(0 0 20px #00ff00);
}

@keyframes heartbeat {
    0%, 100% { 
        transform: scale(1);
        filter: drop-shadow(0 0 20px #00ff00);
    }
    50% { 
        transform: scale(1.1);
        filter: drop-shadow(0 0 30px #00ff00);
    }
}

/* 终端内容 */
.terminal-content {
    text-align: center;
    margin-bottom: 2rem;
}

.typing-text {
    font-size: 1.2rem;
    margin: 0.5rem 0;
    opacity: 0;
    white-space: nowrap;
    overflow: hidden;
    border-right: 2px solid #00ff00;
    animation: typing 3s steps(40) forwards, blink-cursor 1s infinite;
}

@keyframes typing {
    from { 
        width: 0; 
        opacity: 1;
    }
    to { 
        width: 100%; 
        opacity: 1;
    }
}

@keyframes blink-cursor {
    0%, 50% { border-color: #00ff00; }
    51%, 100% { border-color: transparent; }
}

/* 点击提示 */
.click-hint {
    margin: 2rem 0;
    font-size: 1rem;
    text-align: center;
}

.blink {
    animation: blink-text 1.5s infinite;
}

@keyframes blink-text {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

/* 接受按钮 */
.accept-button {
    background: transparent;
    border: 2px solid #00ff00;
    color: #00ff00;
    font-family: 'JetBrains Mono', monospace;
    font-size: 1.5rem;
    padding: 1rem 2rem;
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
    text-transform: uppercase;
}

.accept-button:hover {
    background: #00ff00;
    color: #000;
    box-shadow: 0 0 30px #00ff00;
    transform: scale(1.05);
}

.button-warning {
    position: absolute;
    top: -2rem;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.8rem;
    opacity: 0;
    transition: opacity 0.3s ease;
    white-space: nowrap;
}

.accept-button:hover .button-warning {
    opacity: 1;
}

.hidden {
    display: none !important;
}

/* 成功页面样式 */
#success-page {
    background: #000;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

#fireworks-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.success-content {
    z-index: 2;
    text-align: center;
    color: #00ff00;
}

.congratulations h1 {
    font-size: 4rem;
    margin-bottom: 2rem;
    text-shadow: 0 0 20px #00ff00;
    animation: glow-pulse 2s ease-in-out infinite;
}

@keyframes glow-pulse {
    0%, 100% { text-shadow: 0 0 20px #00ff00; }
    50% { text-shadow: 0 0 40px #00ff00, 0 0 60px #00ff00; }
}

.system-status p {
    font-size: 1.5rem;
    margin: 1rem 0;
    animation: typing 2s steps(30) forwards;
}

.continue-hint {
    margin-top: 3rem;
    font-size: 1.2rem;
}

/* 最终页面样式 */
#final-page {
    background: linear-gradient(45deg, #001100, #002200);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 2rem;
}

.final-content {
    text-align: center;
    max-width: 800px;
}

.love-message h1 {
    font-size: 3rem;
    margin-bottom: 2rem;
    color: #ff6b9d;
    text-shadow: 0 0 20px #ff6b9d;
}

.license-info p {
    font-size: 1.3rem;
    margin: 1rem 0;
    color: #00ff00;
}

.timer-display {
    margin: 2rem 0;
    padding: 1rem;
    border: 2px solid #00ff00;
    border-radius: 10px;
    background: rgba(0, 255, 0, 0.1);
}

.timer-display p {
    font-size: 1.5rem;
    color: #00ff00;
}

#love-timer {
    font-weight: bold;
    color: #ff6b9d;
    text-shadow: 0 0 10px #ff6b9d;
}

.status-bar {
    margin: 2rem auto;
    background: rgba(0, 255, 0, 0.2);
    padding: 1rem 2rem;
    border-radius: 20px;
    font-size: 1.5rem;
    color: #ff6b9d;
    border: 2px solid #00ff00;
    text-align: center;
    max-width: 600px;
    box-shadow: 0 0 20px rgba(0, 255, 0, 0.3);
}

/* 手机适配 */
@media (max-width: 768px) {
    
    .pixel-heart {
        width: 150px;
        height: 150px;
    }
    
    .typing-text {
        font-size: 1rem;
    }
    
    .accept-button {
        font-size: 1.2rem;
        padding: 0.8rem 1.5rem;
    }
    
    .congratulations h1 {
        font-size: 2.5rem;
    }
    
    .love-message h1 {
        font-size: 2rem;
    }
}

/* 特殊效果类 */
.shake {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-10px); }
    75% { transform: translateX(10px); }
}

.broken-heart {
    animation: break-apart 1s ease-out forwards;
}

@keyframes break-apart {
    0% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.2) rotate(10deg); }
    100% { 
        transform: scale(0.8) rotate(-5deg);
        filter: drop-shadow(0 0 10px #ff0000);
        clip-path: polygon(
            30% 25%, 60% 5%, 80% 5%, 100% 30%, 
            90% 50%, 75% 65%, 60% 80%, 40% 65%, 
            25% 50%, 10% 30%, 20% 5%, 40% 5%
        );
    }
}

.static-noise {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100"><defs><filter id="noise"><feTurbulence baseFrequency="0.9"/></filter></defs><rect width="100%" height="100%" filter="url(%23noise)" opacity="0.3"/></svg>');
    animation: static-flicker 0.1s infinite;
    pointer-events: none;
}

@keyframes static-flicker {
    0% { opacity: 0.1; }
    50% { opacity: 0.3; }
    100% { opacity: 0.1; }
}
