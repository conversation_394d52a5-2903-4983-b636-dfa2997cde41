// 全局变量
let clickCount = 0;
let acceptedTime = null;
let timerInterval = null;
let fireworksAnimation = null;

// API配置
const API_BASE_URL = 'https://api.lyclyp.love';

// 页面元素
const initialPage = document.getElementById('initial-page');
const successPage = document.getElementById('success-page');
const finalPage = document.getElementById('final-page');
const pixelHeart = document.getElementById('pixel-heart');
const acceptBtn = document.getElementById('accept-btn');
const clickHint = document.getElementById('click-hint');


// 初始化
document.addEventListener('DOMContentLoaded', async () => {
    // 检查是否已经接受过
    await checkAcceptanceStatus();
    
    // 启动打字机效果
    startTypingAnimation();
    
    // 绑定事件
    bindEvents();
    
    // 移动设备检测逻辑已移动到接受按钮点击后
});

// 检查接受状态
async function checkAcceptanceStatus() {
    try {
        const response = await fetch(`${API_BASE_URL}/api/status`);
        if (response.ok) {
            const data = await response.json();
            if (data.accepted) {
                acceptedTime = new Date(data.acceptedAt);
                showFinalPage();
                return;
            }
        }
    } catch (error) {
        console.log('检查状态失败，继续显示初始页面');
    }
}

// 打字机动画
function startTypingAnimation() {
    const lines = ['line1', 'line2', 'line3', 'line4'];
    const delays = [0, 2000, 4000, 6000];
    
    lines.forEach((lineId, index) => {
        setTimeout(() => {
            const element = document.getElementById(lineId);
            element.style.animationDelay = '0s';
            element.style.opacity = '1';
        }, delays[index]);
    });
    
    // 显示点击提示
    setTimeout(() => {
        clickHint.style.opacity = '1';
    }, 8000);
}

// 绑定事件
function bindEvents() {
    // 全屏点击事件（破解系统）
    document.addEventListener('click', handleScreenClick);
    
    // 接受按钮点击
    acceptBtn.addEventListener('click', handleAcceptClick);
    
    // 键盘事件（成功页面继续）
    document.addEventListener('keydown', handleKeyPress);
}

// 处理屏幕点击（破解逻辑）
function handleScreenClick(e) {
    if (e.target === acceptBtn || !initialPage.classList.contains('active')) {
        return;
    }
    
    clickCount++;
    
    switch (clickCount) {
        case 1:
            // 第一次点击：心跳加快
            document.querySelectorAll('.typing-text').forEach(el => {
                el.classList.add('shake');
            });
            clickHint.innerHTML = '<span class="blink">[ 我的心跳在加速... 再点击一次 ]</span>';

            setTimeout(() => {
                document.querySelectorAll('.typing-text').forEach(el => {
                    el.classList.remove('shake');
                });
            }, 500);
            break;

        case 2:
            // 第二次点击：心变色
            pixelHeart.style.filter = 'drop-shadow(0 0 20px #ff6b9d)';
            clickHint.innerHTML = '<span class="blink">[ 最后一次点击，准备好了吗？ ]</span>';
            break;

        case 3:
            // 第三次点击：显示按钮
            showStaticNoise();
            setTimeout(() => {
                removeStaticNoise();
                showAcceptButton();
            }, 1000);
            break;
    }
}

// 显示静态噪点
function showStaticNoise() {
    const noise = document.createElement('div');
    noise.className = 'static-noise';
    noise.id = 'static-noise';
    document.body.appendChild(noise);
}

// 移除静态噪点
function removeStaticNoise() {
    const noise = document.getElementById('static-noise');
    if (noise) {
        noise.remove();
    }
}

// 显示接受按钮
function showAcceptButton() {
    clickHint.style.display = 'none';
    acceptBtn.classList.remove('hidden');
    acceptBtn.style.animation = 'glow-pulse 1s ease-in-out infinite';
    
    // 播放音效（如果支持）
    playSound('unlock');
}

// 处理接受按钮点击
async function handleAcceptClick() {
    try {
        // 记录接受时间
        const response = await fetch(`${API_BASE_URL}/api/accept`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent
            })
        });
        
        if (response.ok) {
            const data = await response.json();
            acceptedTime = new Date(data.acceptedAt);
        } else {
            // 如果API失败，使用本地时间
            acceptedTime = new Date();
        }
    } catch (error) {
        console.log('API调用失败，使用本地时间');
        acceptedTime = new Date();
    }
    
    // 播放成功音效
    playSound('success');
    
    // 切换到成功页面
    showSuccessPage();
}

// 显示成功页面
function showSuccessPage() {
    initialPage.classList.remove('active');
    successPage.classList.add('active');

    // 启动烟花动画
    startFireworks();

    // 启动运行时间计数器
    startRuntimeCounter();

    // 5秒后自动进入最终页面
    setTimeout(() => {
        showFinalPage();
    }, 5000);
}

// 显示最终页面
function showFinalPage() {
    successPage.classList.remove('active');
    finalPage.classList.add('active');
    
    // 停止烟花动画
    if (fireworksAnimation) {
        cancelAnimationFrame(fireworksAnimation);
    }
    
    // 启动爱情计时器
    startLoveTimer();
}

// 烟花动画
function startFireworks() {
    const canvas = document.getElementById('fireworks-canvas');
    const ctx = canvas.getContext('2d');
    
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;
    
    const fireworks = [];
    const particles = [];
    
    function createFirework() {
        fireworks.push({
            x: Math.random() * canvas.width,
            y: canvas.height,
            targetY: Math.random() * canvas.height * 0.5,
            speed: Math.random() * 3 + 2,
            color: `hsl(${Math.random() * 360}, 100%, 50%)`
        });
    }
    
    function updateFireworks() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // 更新烟花
        for (let i = fireworks.length - 1; i >= 0; i--) {
            const fw = fireworks[i];
            fw.y -= fw.speed;
            
            ctx.fillStyle = fw.color;
            ctx.fillRect(fw.x, fw.y, 3, 3);
            
            if (fw.y <= fw.targetY) {
                // 爆炸
                for (let j = 0; j < 20; j++) {
                    particles.push({
                        x: fw.x,
                        y: fw.y,
                        vx: (Math.random() - 0.5) * 10,
                        vy: (Math.random() - 0.5) * 10,
                        life: 60,
                        color: fw.color
                    });
                }
                fireworks.splice(i, 1);
            }
        }
        
        // 更新粒子
        for (let i = particles.length - 1; i >= 0; i--) {
            const p = particles[i];
            p.x += p.vx;
            p.y += p.vy;
            p.vy += 0.1; // 重力
            p.life--;
            
            ctx.globalAlpha = p.life / 60;
            ctx.fillStyle = p.color;
            ctx.fillRect(p.x, p.y, 2, 2);
            
            if (p.life <= 0) {
                particles.splice(i, 1);
            }
        }
        
        ctx.globalAlpha = 1;
        
        // 随机创建新烟花
        if (Math.random() < 0.1) {
            createFirework();
        }
        
        fireworksAnimation = requestAnimationFrame(updateFireworks);
    }
    
    updateFireworks();
}

// 运行时间计数器
function startRuntimeCounter() {
    const counter = document.getElementById('runtime-counter');
    let seconds = 0;
    
    const interval = setInterval(() => {
        seconds++;
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        counter.textContent = `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }, 1000);
    
    // 5秒后清除
    setTimeout(() => {
        clearInterval(interval);
    }, 5000);
}

// 爱情计时器
function startLoveTimer() {
    if (!acceptedTime) {
        acceptedTime = new Date();
    }
    
    const timerElement = document.getElementById('love-timer');
    
    function updateTimer() {
        const now = new Date();
        const diff = now - acceptedTime;
        
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));
        const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((diff % (1000 * 60)) / 1000);
        
        timerElement.textContent = `${days.toString().padStart(2, '0')}天 ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    
    updateTimer();
    timerInterval = setInterval(updateTimer, 1000);
}

// 键盘事件处理
function handleKeyPress(e) {
    if (successPage.classList.contains('active')) {
        showFinalPage();
    }
}

// 音效播放
function playSound(type) {
    // 使用Web Audio API创建简单的音效
    try {
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        if (type === 'unlock') {
            oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
            oscillator.frequency.exponentialRampToValueAtTime(1200, audioContext.currentTime + 0.1);
        } else if (type === 'success') {
            oscillator.frequency.setValueAtTime(523.25, audioContext.currentTime); // C5
            oscillator.frequency.setValueAtTime(659.25, audioContext.currentTime + 0.1); // E5
            oscillator.frequency.setValueAtTime(783.99, audioContext.currentTime + 0.2); // G5
        }
        
        gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);
        
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.3);
    } catch (error) {
        console.log('音效播放失败');
    }
}



// 窗口大小改变时重新调整canvas
window.addEventListener('resize', () => {
    const canvas = document.getElementById('fireworks-canvas');
    if (canvas) {
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
    }
});
