import type nodeHttp from "node:http";
import { METHODS, STATUS_CODES, maxHeaderSize } from "./internal/http/constants.mjs";
export { METHODS, STATUS_CODES, maxHeaderSize };
export * from "./internal/http/request.mjs";
export * from "./internal/http/response.mjs";
export { Agent } from "./internal/http/agent.mjs";
export declare const createServer: unknown;
export declare const request: unknown;
export declare const get: unknown;
export declare const Server: typeof nodeHttp.Server;
export declare const OutgoingMessage: typeof nodeHttp.OutgoingMessage;
export declare const ClientRequest: typeof nodeHttp.ClientRequest;
export declare const globalAgent: typeof nodeHttp.globalAgent;
export declare const validateHeaderName: unknown;
export declare const validateHeaderValue: unknown;
export declare const setMaxIdleHTTPParsers: unknown;
export declare const _connectionListener: unknown;
export declare const WebSocket: unknown;
export declare const CloseEvent: unknown;
export declare const MessageEvent: unknown;
declare const _default: typeof nodeHttp;
export default _default;
