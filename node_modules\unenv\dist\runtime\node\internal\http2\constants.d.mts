export declare const NGHTTP2_ERR_FRAME_SIZE_ERROR = -522;
export declare const NGHTTP2_SESSION_SERVER = 0;
export declare const NGHTTP2_SESSION_CLIENT = 1;
export declare const NGHTTP2_STREAM_STATE_IDLE = 1;
export declare const NGHTTP2_STREAM_STATE_OPEN = 2;
export declare const NGHTTP2_STREAM_STATE_RESERVED_LOCAL = 3;
export declare const NGHTTP2_STREAM_STATE_RESERVED_REMOTE = 4;
export declare const NGHTTP2_STREAM_STATE_HALF_CLOSED_LOCAL = 5;
export declare const NGHTTP2_STREAM_STATE_HALF_CLOSED_REMOTE = 6;
export declare const NGHTTP2_STREAM_STATE_CLOSED = 7;
export declare const NGHTTP2_FLAG_NONE = 0;
export declare const NGHTTP2_FLAG_END_STREAM = 1;
export declare const NGHTTP2_FLAG_END_HEADERS = 4;
export declare const NGHTTP2_FLAG_ACK = 1;
export declare const NGHTTP2_FLAG_PADDED = 8;
export declare const NGHTTP2_FLAG_PRIORITY = 32;
export declare const DEFAULT_SETTINGS_HEADER_TABLE_SIZE = 4096;
export declare const DEFAULT_SETTINGS_ENABLE_PUSH = 1;
export declare const DEFAULT_SETTINGS_MAX_CONCURRENT_STREAMS = 4294967295;
export declare const DEFAULT_SETTINGS_INITIAL_WINDOW_SIZE = 65535;
export declare const DEFAULT_SETTINGS_MAX_FRAME_SIZE = 16384;
export declare const DEFAULT_SETTINGS_MAX_HEADER_LIST_SIZE = 65535;
export declare const DEFAULT_SETTINGS_ENABLE_CONNECT_PROTOCOL = 0;
export declare const MAX_MAX_FRAME_SIZE = 16777215;
export declare const MIN_MAX_FRAME_SIZE = 16384;
export declare const MAX_INITIAL_WINDOW_SIZE = 2147483647;
export declare const NGHTTP2_SETTINGS_HEADER_TABLE_SIZE = 1;
export declare const NGHTTP2_SETTINGS_ENABLE_PUSH = 2;
export declare const NGHTTP2_SETTINGS_MAX_CONCURRENT_STREAMS = 3;
export declare const NGHTTP2_SETTINGS_INITIAL_WINDOW_SIZE = 4;
export declare const NGHTTP2_SETTINGS_MAX_FRAME_SIZE = 5;
export declare const NGHTTP2_SETTINGS_MAX_HEADER_LIST_SIZE = 6;
export declare const NGHTTP2_SETTINGS_ENABLE_CONNECT_PROTOCOL = 8;
export declare const PADDING_STRATEGY_NONE = 0;
export declare const PADDING_STRATEGY_ALIGNED = 1;
export declare const PADDING_STRATEGY_MAX = 2;
export declare const PADDING_STRATEGY_CALLBACK = 1;
export declare const NGHTTP2_NO_ERROR = 0;
export declare const NGHTTP2_PROTOCOL_ERROR = 1;
export declare const NGHTTP2_INTERNAL_ERROR = 2;
export declare const NGHTTP2_FLOW_CONTROL_ERROR = 3;
export declare const NGHTTP2_SETTINGS_TIMEOUT = 4;
export declare const NGHTTP2_STREAM_CLOSED = 5;
export declare const NGHTTP2_FRAME_SIZE_ERROR = 6;
export declare const NGHTTP2_REFUSED_STREAM = 7;
export declare const NGHTTP2_CANCEL = 8;
export declare const NGHTTP2_COMPRESSION_ERROR = 9;
export declare const NGHTTP2_CONNECT_ERROR = 10;
export declare const NGHTTP2_ENHANCE_YOUR_CALM = 11;
export declare const NGHTTP2_INADEQUATE_SECURITY = 12;
export declare const NGHTTP2_HTTP_1_1_REQUIRED = 13;
export declare const NGHTTP2_DEFAULT_WEIGHT = 16;
export declare const HTTP2_HEADER_STATUS = ":status";
export declare const HTTP2_HEADER_METHOD = ":method";
export declare const HTTP2_HEADER_AUTHORITY = ":authority";
export declare const HTTP2_HEADER_SCHEME = ":scheme";
export declare const HTTP2_HEADER_PATH = ":path";
export declare const HTTP2_HEADER_PROTOCOL = ":protocol";
export declare const HTTP2_HEADER_ACCEPT_ENCODING = "accept-encoding";
export declare const HTTP2_HEADER_ACCEPT_LANGUAGE = "accept-language";
export declare const HTTP2_HEADER_ACCEPT_RANGES = "accept-ranges";
export declare const HTTP2_HEADER_ACCEPT = "accept";
export declare const HTTP2_HEADER_ACCESS_CONTROL_ALLOW_CREDENTIALS = "access-control-allow-credentials";
export declare const HTTP2_HEADER_ACCESS_CONTROL_ALLOW_HEADERS = "access-control-allow-headers";
export declare const HTTP2_HEADER_ACCESS_CONTROL_ALLOW_METHODS = "access-control-allow-methods";
export declare const HTTP2_HEADER_ACCESS_CONTROL_ALLOW_ORIGIN = "access-control-allow-origin";
export declare const HTTP2_HEADER_ACCESS_CONTROL_EXPOSE_HEADERS = "access-control-expose-headers";
export declare const HTTP2_HEADER_ACCESS_CONTROL_REQUEST_HEADERS = "access-control-request-headers";
export declare const HTTP2_HEADER_ACCESS_CONTROL_REQUEST_METHOD = "access-control-request-method";
export declare const HTTP2_HEADER_AGE = "age";
export declare const HTTP2_HEADER_AUTHORIZATION = "authorization";
export declare const HTTP2_HEADER_CACHE_CONTROL = "cache-control";
export declare const HTTP2_HEADER_CONNECTION = "connection";
export declare const HTTP2_HEADER_CONTENT_DISPOSITION = "content-disposition";
export declare const HTTP2_HEADER_CONTENT_ENCODING = "content-encoding";
export declare const HTTP2_HEADER_CONTENT_LENGTH = "content-length";
export declare const HTTP2_HEADER_CONTENT_TYPE = "content-type";
export declare const HTTP2_HEADER_COOKIE = "cookie";
export declare const HTTP2_HEADER_DATE = "date";
export declare const HTTP2_HEADER_ETAG = "etag";
export declare const HTTP2_HEADER_FORWARDED = "forwarded";
export declare const HTTP2_HEADER_HOST = "host";
export declare const HTTP2_HEADER_IF_MODIFIED_SINCE = "if-modified-since";
export declare const HTTP2_HEADER_IF_NONE_MATCH = "if-none-match";
export declare const HTTP2_HEADER_IF_RANGE = "if-range";
export declare const HTTP2_HEADER_LAST_MODIFIED = "last-modified";
export declare const HTTP2_HEADER_LINK = "link";
export declare const HTTP2_HEADER_LOCATION = "location";
export declare const HTTP2_HEADER_RANGE = "range";
export declare const HTTP2_HEADER_REFERER = "referer";
export declare const HTTP2_HEADER_SERVER = "server";
export declare const HTTP2_HEADER_SET_COOKIE = "set-cookie";
export declare const HTTP2_HEADER_STRICT_TRANSPORT_SECURITY = "strict-transport-security";
export declare const HTTP2_HEADER_TRANSFER_ENCODING = "transfer-encoding";
export declare const HTTP2_HEADER_TE = "te";
export declare const HTTP2_HEADER_UPGRADE_INSECURE_REQUESTS = "upgrade-insecure-requests";
export declare const HTTP2_HEADER_UPGRADE = "upgrade";
export declare const HTTP2_HEADER_USER_AGENT = "user-agent";
export declare const HTTP2_HEADER_VARY = "vary";
export declare const HTTP2_HEADER_X_CONTENT_TYPE_OPTIONS = "x-content-type-options";
export declare const HTTP2_HEADER_X_FRAME_OPTIONS = "x-frame-options";
export declare const HTTP2_HEADER_KEEP_ALIVE = "keep-alive";
export declare const HTTP2_HEADER_PROXY_CONNECTION = "proxy-connection";
export declare const HTTP2_HEADER_X_XSS_PROTECTION = "x-xss-protection";
export declare const HTTP2_HEADER_ALT_SVC = "alt-svc";
export declare const HTTP2_HEADER_CONTENT_SECURITY_POLICY = "content-security-policy";
export declare const HTTP2_HEADER_EARLY_DATA = "early-data";
export declare const HTTP2_HEADER_EXPECT_CT = "expect-ct";
export declare const HTTP2_HEADER_ORIGIN = "origin";
export declare const HTTP2_HEADER_PURPOSE = "purpose";
export declare const HTTP2_HEADER_TIMING_ALLOW_ORIGIN = "timing-allow-origin";
export declare const HTTP2_HEADER_X_FORWARDED_FOR = "x-forwarded-for";
export declare const HTTP2_HEADER_PRIORITY = "priority";
export declare const HTTP2_HEADER_ACCEPT_CHARSET = "accept-charset";
export declare const HTTP2_HEADER_ACCESS_CONTROL_MAX_AGE = "access-control-max-age";
export declare const HTTP2_HEADER_ALLOW = "allow";
export declare const HTTP2_HEADER_CONTENT_LANGUAGE = "content-language";
export declare const HTTP2_HEADER_CONTENT_LOCATION = "content-location";
export declare const HTTP2_HEADER_CONTENT_MD5 = "content-md5";
export declare const HTTP2_HEADER_CONTENT_RANGE = "content-range";
export declare const HTTP2_HEADER_DNT = "dnt";
export declare const HTTP2_HEADER_EXPECT = "expect";
export declare const HTTP2_HEADER_EXPIRES = "expires";
export declare const HTTP2_HEADER_FROM = "from";
export declare const HTTP2_HEADER_IF_MATCH = "if-match";
export declare const HTTP2_HEADER_IF_UNMODIFIED_SINCE = "if-unmodified-since";
export declare const HTTP2_HEADER_MAX_FORWARDS = "max-forwards";
export declare const HTTP2_HEADER_PREFER = "prefer";
export declare const HTTP2_HEADER_PROXY_AUTHENTICATE = "proxy-authenticate";
export declare const HTTP2_HEADER_PROXY_AUTHORIZATION = "proxy-authorization";
export declare const HTTP2_HEADER_REFRESH = "refresh";
export declare const HTTP2_HEADER_RETRY_AFTER = "retry-after";
export declare const HTTP2_HEADER_TRAILER = "trailer";
export declare const HTTP2_HEADER_TK = "tk";
export declare const HTTP2_HEADER_VIA = "via";
export declare const HTTP2_HEADER_WARNING = "warning";
export declare const HTTP2_HEADER_WWW_AUTHENTICATE = "www-authenticate";
export declare const HTTP2_HEADER_HTTP2_SETTINGS = "http2-settings";
export declare const HTTP2_METHOD_ACL = "ACL";
export declare const HTTP2_METHOD_BASELINE_CONTROL = "BASELINE-CONTROL";
export declare const HTTP2_METHOD_BIND = "BIND";
export declare const HTTP2_METHOD_CHECKIN = "CHECKIN";
export declare const HTTP2_METHOD_CHECKOUT = "CHECKOUT";
export declare const HTTP2_METHOD_CONNECT = "CONNECT";
export declare const HTTP2_METHOD_COPY = "COPY";
export declare const HTTP2_METHOD_DELETE = "DELETE";
export declare const HTTP2_METHOD_GET = "GET";
export declare const HTTP2_METHOD_HEAD = "HEAD";
export declare const HTTP2_METHOD_LABEL = "LABEL";
export declare const HTTP2_METHOD_LINK = "LINK";
export declare const HTTP2_METHOD_LOCK = "LOCK";
export declare const HTTP2_METHOD_MERGE = "MERGE";
export declare const HTTP2_METHOD_MKACTIVITY = "MKACTIVITY";
export declare const HTTP2_METHOD_MKCALENDAR = "MKCALENDAR";
export declare const HTTP2_METHOD_MKCOL = "MKCOL";
export declare const HTTP2_METHOD_MKREDIRECTREF = "MKREDIRECTREF";
export declare const HTTP2_METHOD_MKWORKSPACE = "MKWORKSPACE";
export declare const HTTP2_METHOD_MOVE = "MOVE";
export declare const HTTP2_METHOD_OPTIONS = "OPTIONS";
export declare const HTTP2_METHOD_ORDERPATCH = "ORDERPATCH";
export declare const HTTP2_METHOD_PATCH = "PATCH";
export declare const HTTP2_METHOD_POST = "POST";
export declare const HTTP2_METHOD_PRI = "PRI";
export declare const HTTP2_METHOD_PROPFIND = "PROPFIND";
export declare const HTTP2_METHOD_PROPPATCH = "PROPPATCH";
export declare const HTTP2_METHOD_PUT = "PUT";
export declare const HTTP2_METHOD_REBIND = "REBIND";
export declare const HTTP2_METHOD_REPORT = "REPORT";
export declare const HTTP2_METHOD_SEARCH = "SEARCH";
export declare const HTTP2_METHOD_TRACE = "TRACE";
export declare const HTTP2_METHOD_UNBIND = "UNBIND";
export declare const HTTP2_METHOD_UNCHECKOUT = "UNCHECKOUT";
export declare const HTTP2_METHOD_UNLINK = "UNLINK";
export declare const HTTP2_METHOD_UNLOCK = "UNLOCK";
export declare const HTTP2_METHOD_UPDATE = "UPDATE";
export declare const HTTP2_METHOD_UPDATEREDIRECTREF = "UPDATEREDIRECTREF";
export declare const HTTP2_METHOD_VERSION_CONTROL = "VERSION-CONTROL";
export declare const HTTP_STATUS_CONTINUE = 100;
export declare const HTTP_STATUS_SWITCHING_PROTOCOLS = 101;
export declare const HTTP_STATUS_PROCESSING = 102;
export declare const HTTP_STATUS_EARLY_HINTS = 103;
export declare const HTTP_STATUS_OK = 200;
export declare const HTTP_STATUS_CREATED = 201;
export declare const HTTP_STATUS_ACCEPTED = 202;
export declare const HTTP_STATUS_NON_AUTHORITATIVE_INFORMATION = 203;
export declare const HTTP_STATUS_NO_CONTENT = 204;
export declare const HTTP_STATUS_RESET_CONTENT = 205;
export declare const HTTP_STATUS_PARTIAL_CONTENT = 206;
export declare const HTTP_STATUS_MULTI_STATUS = 207;
export declare const HTTP_STATUS_ALREADY_REPORTED = 208;
export declare const HTTP_STATUS_IM_USED = 226;
export declare const HTTP_STATUS_MULTIPLE_CHOICES = 300;
export declare const HTTP_STATUS_MOVED_PERMANENTLY = 301;
export declare const HTTP_STATUS_FOUND = 302;
export declare const HTTP_STATUS_SEE_OTHER = 303;
export declare const HTTP_STATUS_NOT_MODIFIED = 304;
export declare const HTTP_STATUS_USE_PROXY = 305;
export declare const HTTP_STATUS_TEMPORARY_REDIRECT = 307;
export declare const HTTP_STATUS_PERMANENT_REDIRECT = 308;
export declare const HTTP_STATUS_BAD_REQUEST = 400;
export declare const HTTP_STATUS_UNAUTHORIZED = 401;
export declare const HTTP_STATUS_PAYMENT_REQUIRED = 402;
export declare const HTTP_STATUS_FORBIDDEN = 403;
export declare const HTTP_STATUS_NOT_FOUND = 404;
export declare const HTTP_STATUS_METHOD_NOT_ALLOWED = 405;
export declare const HTTP_STATUS_NOT_ACCEPTABLE = 406;
export declare const HTTP_STATUS_PROXY_AUTHENTICATION_REQUIRED = 407;
export declare const HTTP_STATUS_REQUEST_TIMEOUT = 408;
export declare const HTTP_STATUS_CONFLICT = 409;
export declare const HTTP_STATUS_GONE = 410;
export declare const HTTP_STATUS_LENGTH_REQUIRED = 411;
export declare const HTTP_STATUS_PRECONDITION_FAILED = 412;
export declare const HTTP_STATUS_PAYLOAD_TOO_LARGE = 413;
export declare const HTTP_STATUS_URI_TOO_LONG = 414;
export declare const HTTP_STATUS_UNSUPPORTED_MEDIA_TYPE = 415;
export declare const HTTP_STATUS_RANGE_NOT_SATISFIABLE = 416;
export declare const HTTP_STATUS_EXPECTATION_FAILED = 417;
export declare const HTTP_STATUS_TEAPOT = 418;
export declare const HTTP_STATUS_MISDIRECTED_REQUEST = 421;
export declare const HTTP_STATUS_UNPROCESSABLE_ENTITY = 422;
export declare const HTTP_STATUS_LOCKED = 423;
export declare const HTTP_STATUS_FAILED_DEPENDENCY = 424;
export declare const HTTP_STATUS_TOO_EARLY = 425;
export declare const HTTP_STATUS_UPGRADE_REQUIRED = 426;
export declare const HTTP_STATUS_PRECONDITION_REQUIRED = 428;
export declare const HTTP_STATUS_TOO_MANY_REQUESTS = 429;
export declare const HTTP_STATUS_REQUEST_HEADER_FIELDS_TOO_LARGE = 431;
export declare const HTTP_STATUS_UNAVAILABLE_FOR_LEGAL_REASONS = 451;
export declare const HTTP_STATUS_INTERNAL_SERVER_ERROR = 500;
export declare const HTTP_STATUS_NOT_IMPLEMENTED = 501;
export declare const HTTP_STATUS_BAD_GATEWAY = 502;
export declare const HTTP_STATUS_SERVICE_UNAVAILABLE = 503;
export declare const HTTP_STATUS_GATEWAY_TIMEOUT = 504;
export declare const HTTP_STATUS_HTTP_VERSION_NOT_SUPPORTED = 505;
export declare const HTTP_STATUS_VARIANT_ALSO_NEGOTIATES = 506;
export declare const HTTP_STATUS_INSUFFICIENT_STORAGE = 507;
export declare const HTTP_STATUS_LOOP_DETECTED = 508;
export declare const HTTP_STATUS_BANDWIDTH_LIMIT_EXCEEDED = 509;
export declare const HTTP_STATUS_NOT_EXTENDED = 510;
export declare const HTTP_STATUS_NETWORK_AUTHENTICATION_REQUIRED = 511;
